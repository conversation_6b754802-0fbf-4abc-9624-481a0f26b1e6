#!/usr/bin/env python3
"""
Example queries for the durian export database showing how to work with Unix timestamps
"""

import sqlite3
from datetime import datetime, timedelta
import time

def connect_db():
    """Connect to the database"""
    return sqlite3.connect('durian_export.db')

def show_timestamp_examples():
    """Show how Unix timestamps are stored and converted"""
    conn = connect_db()
    cursor = conn.cursor()
    
    print("=== Unix Timestamp Examples ===")
    
    # Show raw timestamps vs readable dates
    cursor.execute("""
        SELECT export_id, timestamp, datetime(timestamp, 'unixepoch') as readable_date
        FROM farm_exports 
        LIMIT 5
    """)
    
    print("Export ID                            | Unix Timestamp | Readable Date")
    print("-" * 70)
    for export_id, timestamp, readable_date in cursor.fetchall():
        print(f"{export_id[:8]}... | {timestamp:>13} | {readable_date}")
    
    conn.close()

def monthly_success_rates():
    """Show monthly success rates using Unix timestamps"""
    conn = connect_db()
    cursor = conn.cursor()
    
    print("\n=== Monthly Success Rates (Using Unix Timestamps) ===")
    
    cursor.execute("""
        SELECT 
            strftime('%Y-%m', datetime(timestamp, 'unixepoch')) as month,
            COUNT(*) as total_exports,
            SUM(CASE WHEN export_success = 1 THEN 1 ELSE 0 END) as successful_exports,
            ROUND(SUM(CASE WHEN export_success = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 1) as success_rate
        FROM farm_exports 
        GROUP BY strftime('%Y-%m', datetime(timestamp, 'unixepoch'))
        ORDER BY month
    """)
    
    print("Month    | Total | Success | Rate")
    print("-" * 35)
    for month, total, successful, rate in cursor.fetchall():
        print(f"{month} | {total:>5} | {successful:>7} | {rate:>4}%")
    
    conn.close()

def query_by_date_range():
    """Show how to query by date range using Unix timestamps"""
    conn = connect_db()
    cursor = conn.cursor()
    
    print("\n=== Query by Date Range (Unix Timestamps) ===")
    
    # Example: Get exports from last 90 days
    now = int(time.time())
    ninety_days_ago = now - (90 * 24 * 3600)
    
    cursor.execute("""
        SELECT 
            COUNT(*) as total_exports,
            SUM(CASE WHEN export_success = 1 THEN 1 ELSE 0 END) as successful_exports,
            ROUND(SUM(CASE WHEN export_success = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 1) as success_rate
        FROM farm_exports 
        WHERE timestamp >= ?
    """, (ninety_days_ago,))
    
    total, successful, rate = cursor.fetchone()
    print(f"Last 90 days: {successful}/{total} exports successful ({rate}%)")
    
    # Example: Get exports from specific month (January 2025)
    # Convert date to Unix timestamp
    jan_start = int(datetime(2025, 1, 1).timestamp())
    jan_end = int(datetime(2025, 2, 1).timestamp())
    
    cursor.execute("""
        SELECT 
            COUNT(*) as total_exports,
            SUM(CASE WHEN export_success = 1 THEN 1 ELSE 0 END) as successful_exports
        FROM farm_exports 
        WHERE timestamp >= ? AND timestamp < ?
    """, (jan_start, jan_end))
    
    total, successful = cursor.fetchone()
    rate = (successful / total * 100) if total > 0 else 0
    print(f"January 2025: {successful}/{total} exports successful ({rate:.1f}%)")
    
    conn.close()

def etrace_vs_non_etrace_by_month():
    """Compare E-trace vs Non-E-trace farms by month"""
    conn = connect_db()
    cursor = conn.cursor()
    
    print("\n=== E-trace vs Non-E-trace Success Rates by Month ===")
    
    cursor.execute("""
        SELECT 
            strftime('%Y-%m', datetime(fe.timestamp, 'unixepoch')) as month,
            f.uses_etrace,
            COUNT(*) as total_exports,
            SUM(CASE WHEN fe.export_success = 1 THEN 1 ELSE 0 END) as successful_exports,
            ROUND(SUM(CASE WHEN fe.export_success = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 1) as success_rate
        FROM farm_exports fe
        JOIN farms f ON fe.farm_id = f.id
        GROUP BY strftime('%Y-%m', datetime(fe.timestamp, 'unixepoch')), f.uses_etrace
        ORDER BY month, f.uses_etrace DESC
    """)
    
    print("Month    | Type        | Total | Success | Rate")
    print("-" * 50)
    for month, uses_etrace, total, successful, rate in cursor.fetchall():
        farm_type = "E-trace    " if uses_etrace else "Non-E-trace"
        print(f"{month} | {farm_type} | {total:>5} | {successful:>7} | {rate:>4}%")
    
    conn.close()

def main():
    """Run all examples"""
    show_timestamp_examples()
    monthly_success_rates()
    query_by_date_range()
    etrace_vs_non_etrace_by_month()

if __name__ == "__main__":
    main()
