-- SQL Query: Which packing houses have shown the most improvement in reducing rejection rates compared to last month?

WITH monthly_rejection_rates AS (
    SELECT 
        es.packing_house_id,
        ph.name as packing_house_name,
        ph.owner as packing_house_owner,
        strftime('%Y-%m', datetime(es.timestamp, 'unixepoch')) as month,
        COUNT(*) as total_shipments,
        SUM(CASE WHEN es.status = 'rejected' THEN 1 ELSE 0 END) as rejected_shipments,
        ROUND(SUM(CASE WHEN es.status = 'rejected' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as rejection_rate
    FROM export_shipments es
    JOIN packing_houses ph ON es.packing_house_id = ph.id
    WHERE strftime('%Y-%m', datetime(es.timestamp, 'unixepoch')) >= strftime('%Y-%m', datetime('now', '-2 months'))
    GROUP BY es.packing_house_id, ph.name, ph.owner, strftime('%Y-%m', datetime(es.timestamp, 'unixepoch'))
    HAVING COUNT(*) >= 10  -- Only include packing houses with at least 10 shipments per month
),
current_vs_previous AS (
    SELECT 
        packing_house_id,
        packing_house_name,
        packing_house_owner,
        LAG(rejection_rate) OVER (PARTITION BY packing_house_id ORDER BY month) as previous_month_rejection_rate,
        rejection_rate as current_month_rejection_rate,
        LAG(total_shipments) OVER (PARTITION BY packing_house_id ORDER BY month) as previous_month_shipments,
        total_shipments as current_month_shipments,
        month
    FROM monthly_rejection_rates
)
SELECT 
    packing_house_name as 'Packing House',
    packing_house_owner as 'Owner',
    previous_month_rejection_rate as 'Previous Month Rejection Rate (%)',
    current_month_rejection_rate as 'Current Month Rejection Rate (%)',
    ROUND(previous_month_rejection_rate - current_month_rejection_rate, 2) as 'Improvement (% Points)',
    previous_month_shipments as 'Previous Month Shipments',
    current_month_shipments as 'Current Month Shipments',
    month as 'Current Month'
FROM current_vs_previous
WHERE previous_month_rejection_rate IS NOT NULL
    AND previous_month_rejection_rate > current_month_rejection_rate  -- Only show improvements
ORDER BY (previous_month_rejection_rate - current_month_rejection_rate) DESC
LIMIT 10;

-- Alternative simpler version for specific months:
/*
WITH last_two_months AS (
    SELECT DISTINCT strftime('%Y-%m', datetime(timestamp, 'unixepoch')) as month
    FROM export_shipments 
    ORDER BY month DESC 
    LIMIT 2
),
monthly_stats AS (
    SELECT 
        es.packing_house_id,
        ph.name as packing_house_name,
        ph.owner as packing_house_owner,
        strftime('%Y-%m', datetime(es.timestamp, 'unixepoch')) as month,
        COUNT(*) as total_shipments,
        ROUND(SUM(CASE WHEN es.status = 'rejected' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as rejection_rate
    FROM export_shipments es
    JOIN packing_houses ph ON es.packing_house_id = ph.id
    WHERE strftime('%Y-%m', datetime(es.timestamp, 'unixepoch')) IN (SELECT month FROM last_two_months)
    GROUP BY es.packing_house_id, ph.name, ph.owner, strftime('%Y-%m', datetime(es.timestamp, 'unixepoch'))
    HAVING COUNT(*) >= 5  -- At least 5 shipments per month
)
SELECT 
    prev.packing_house_name as 'Packing House',
    prev.packing_house_owner as 'Owner',
    prev.rejection_rate as 'Previous Month Rejection Rate (%)',
    curr.rejection_rate as 'Current Month Rejection Rate (%)',
    ROUND(prev.rejection_rate - curr.rejection_rate, 2) as 'Improvement (% Points)',
    prev.total_shipments as 'Previous Month Shipments',
    curr.total_shipments as 'Current Month Shipments'
FROM monthly_stats prev
JOIN monthly_stats curr ON prev.packing_house_id = curr.packing_house_id
WHERE prev.month < curr.month  -- Previous month vs current month
    AND prev.rejection_rate > curr.rejection_rate  -- Only improvements
ORDER BY (prev.rejection_rate - curr.rejection_rate) DESC
LIMIT 10;
*/
