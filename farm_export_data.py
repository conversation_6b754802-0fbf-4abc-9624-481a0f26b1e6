import uuid
import random
import csv
from datetime import datetime, timedelta

# Configuration
TOTAL_RECORDS = 80000  # Number of export records to generate
FARMS_CSV = 'durian_farms.csv'
OUTPUT_CSV = 'farm_export_data.csv'

# Export success distribution based on user preferences
# E-trace farms: 70% succeed, 30% fail
# Non-E-trace farms: 40% succeed, 60% fail
ETRACE_SUCCESS_RATE = 0.70
NON_ETRACE_SUCCESS_RATE = 0.40

def load_farm_data():
    """Load farm data from the existing CSV file"""
    farms = []
    etrace_farms = []
    non_etrace_farms = []

    try:
        with open(FARMS_CSV, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                farm_data = {
                    'id': row['id'],
                    'uses_etrace': row['uses_etrace'].lower() == 'true'
                }
                farms.append(farm_data)

                if farm_data['uses_etrace']:
                    etrace_farms.append(farm_data)
                else:
                    non_etrace_farms.append(farm_data)

        print(f"Loaded {len(farms)} farms")
        print(f"E-trace farms: {len(etrace_farms)} ({len(etrace_farms)/len(farms)*100:.1f}%)")
        print(f"Non-E-trace farms: {len(non_etrace_farms)} ({len(non_etrace_farms)/len(farms)*100:.1f}%)")

        return farms, etrace_farms, non_etrace_farms
    except FileNotFoundError:
        print(f"Error: {FARMS_CSV} not found. Please run durian_farms.py first.")
        return [], [], []

def generate_timestamp_last_12_months():
    """Generate a random timestamp within the last 12 months"""
    now = datetime.now()
    twelve_months_ago = now - timedelta(days=365)

    # Generate random time between 12 months ago and now
    time_diff = now - twelve_months_ago
    random_seconds = random.randint(0, int(time_diff.total_seconds()))
    random_time = twelve_months_ago + timedelta(seconds=random_seconds)

    # Convert to Unix timestamp
    return int(random_time.timestamp())

def determine_export_success(farm):
    """Determine export success based on farm's e-trace status"""
    if farm['uses_etrace']:
        return random.random() < ETRACE_SUCCESS_RATE
    else:
        return random.random() < NON_ETRACE_SUCCESS_RATE

def generate_export_data(farms, num_records):
    """Generate farm export records"""
    if not farms:
        return []

    exports = []

    print(f"Generating {num_records} export records...")

    for i in range(num_records):
        if i % 5000 == 0:
            print(f"Generated {i}/{num_records} records...")

        # Generate export ID
        export_id = str(uuid.uuid4())

        # Select random farm
        farm = random.choice(farms)

        # Determine export success
        export_success = determine_export_success(farm)

        # Generate timestamp
        timestamp = generate_timestamp_last_12_months()

        export_record = {
            'export_id': export_id,
            'farm_id': farm['id'],
            'export_success': export_success,
            'timestamp': timestamp
        }

        exports.append(export_record)

    return exports

def save_exports_to_csv(exports, filename):
    """Save export records to CSV file"""
    fieldnames = ['export_id', 'farm_id', 'export_success', 'timestamp']

    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()

        for export in exports:
            writer.writerow(export)

def print_statistics(exports, etrace_farms, non_etrace_farms):
    """Print statistics about generated export records"""
    total = len(exports)

    # Overall statistics
    success_count = sum(1 for e in exports if e['export_success'])
    fail_count = total - success_count

    print(f"\n=== Export Statistics ===")
    print(f"Total exports: {total:,}")
    print(f"Successful: {success_count:,} ({success_count/total*100:.1f}%)")
    print(f"Failed: {fail_count:,} ({fail_count/total*100:.1f}%)")

    # Statistics by farm type
    etrace_farm_ids = {farm['id'] for farm in etrace_farms}
    non_etrace_farm_ids = {farm['id'] for farm in non_etrace_farms}

    etrace_exports = [e for e in exports if e['farm_id'] in etrace_farm_ids]
    non_etrace_exports = [e for e in exports if e['farm_id'] in non_etrace_farm_ids]

    etrace_success = sum(1 for e in etrace_exports if e['export_success'])
    non_etrace_success = sum(1 for e in non_etrace_exports if e['export_success'])

    print(f"\n=== E-trace Farm Exports ===")
    print(f"Total: {len(etrace_exports):,}")
    print(f"Successful: {etrace_success:,} ({etrace_success/len(etrace_exports)*100:.1f}%)")
    print(f"Failed: {len(etrace_exports)-etrace_success:,} ({(len(etrace_exports)-etrace_success)/len(etrace_exports)*100:.1f}%)")

    print(f"\n=== Non-E-trace Farm Exports ===")
    print(f"Total: {len(non_etrace_exports):,}")
    print(f"Successful: {non_etrace_success:,} ({non_etrace_success/len(non_etrace_exports)*100:.1f}%)")
    print(f"Failed: {len(non_etrace_exports)-non_etrace_success:,} ({(len(non_etrace_exports)-non_etrace_success)/len(non_etrace_exports)*100:.1f}%)")

def main():
    print("=== Farm Export Data Generator ===")
    print("E-trace farms: 70% success rate")
    print("Non-E-trace farms: 40% success rate")

    # Load farm data
    farms, etrace_farms, non_etrace_farms = load_farm_data()
    if not farms:
        return

    # Generate export records
    exports = generate_export_data(farms, TOTAL_RECORDS)

    if not exports:
        print("No export records generated.")
        return

    # Save to CSV
    save_exports_to_csv(exports, OUTPUT_CSV)
    print(f"\nExport records saved to {OUTPUT_CSV}")

    # Print statistics
    print_statistics(exports, etrace_farms, non_etrace_farms)

    # Show sample records
    print(f"\n=== Sample Records ===")
    for i, export in enumerate(exports[:5]):
        print(f"Export {i+1}:")
        print(f"  ID: {export['export_id']}")
        print(f"  Farm ID: {export['farm_id']}")
        print(f"  Success: {export['export_success']}")
        # Convert timestamp to readable format for display
        readable_time = datetime.fromtimestamp(export['timestamp']).strftime('%Y-%m-%d %H:%M:%S')
        print(f"  Timestamp: {export['timestamp']} ({readable_time})")
        print("-" * 50)

if __name__ == "__main__":
    main()
