import uuid
import random
import csv

# Thai first and last names (with Thai script)
thai_first_names = ["สมชาย", "สมศรี", "สมนึก", "สมใจ", "สมพร", "สมหมาย", "สมบัติ",
                    "พรนิภา", "มานะ", "ชัยวัฒน์", "วิชิตา", "ปราณี", "สุวรรณ", "อนงค์",
                    "วิไล", "สุนีย์", "ประยุทธ", "นิรันดร", "สุภาพ", "วิทยา", "กิตติ", "ชาติ",
                    "สุรีย์", "วิมล", "ประสิทธิ์", "สุรพล", "วิชัย", "สุพจน์", "ประเสริฐ", "วิรัช"]
thai_last_names = ["สุขสันต์", "วงศ์ชัยวัฒน์", "แซ่ตั้ง", "เรืองไกร", "ทองคำ", "ทองดี",
                   "ไชยชนะ", "ชัยวัฒนตน์", "ยอดชัยวัฒน์", "ใจดี", "สุขใส", "ถาวรวงศ์",
                   "สุขสวัสดิ์", "เจริญสุข", "มั่งมี", "รุ่งเรือง", "เจริญผล", "บุญมี",
                   "ศรีสุข", "ทองใส", "เพชรดี", "มณีใส", "ทรัพย์สิน", "สมบูรณ์"]

# Thai provinces known for durian (with Thai script)
provinces = ["ชุมพร", "ระยอง", "ตราด", "กาญจนบุรี", "ประจวบคีรีขันธ์", "นครศรีธรรมราช",
             "ยะลา", "ปัตตานี", "นราธิวาส", "สุราษฎร์ธานี", "กระบี่", "สงขลา"]

tambons = ["บ้านใหม่", "ท่าช้าง", "มะขาม", "วังน้ำเย็น", "ท่าตะเภา", "บางพระ", "หนองเบญจา", "คลองใหญ่",
           "บ้านเก่า", "หนองบัว", "คลองท่อม", "ท่าศาลา", "บางกุ้ง", "หนองปลิง", "คลองแดน", "บ้านโป่ง"]

# Generate 200 farms
farms = []
for i in range(200):
    # Generate UUID
    farm_id = uuid.uuid4()

    # Generate owner name
    thai_first = random.choice(thai_first_names)
    thai_last = random.choice(thai_last_names)
    owner = f"{thai_first} {thai_last}"

    # Generate farm name
    name = f"ฟาร์มทุเรียน{thai_first}"

    # Generate address
    province = random.choice(provinces)
    tambon = random.choice(tambons)
    address = f"เลขที่ {random.randint(1, 999)} หมู่ที่ {random.randint(1, 15)}, ตำบล{tambon}, จังหวัด{province}"

    # Generate uses_etrace flag (approximately 30% of farms use e-trace)
    uses_etrace = random.random() < 0.3

    farms.append({
        "id": farm_id,
        "name": name,
        "address": address,
        "owner": owner,
        "uses_etrace": uses_etrace
    })

# Save to CSV file
with open('durian_farms.csv', 'w', newline='', encoding='utf-8') as csvfile:
    fieldnames = ['id', 'name', 'address', 'owner', 'uses_etrace']
    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

    writer.writeheader()
    for farm in farms:
        writer.writerow(farm)

print(f"Generated 200 durian farms and saved to durian_farms.csv")

# Print statistics
etrace_count = sum(1 for farm in farms if farm['uses_etrace'])
non_etrace_count = len(farms) - etrace_count

print(f"\n=== Farm Statistics ===")
print(f"Total farms: {len(farms)}")
print(f"E-trace farms: {etrace_count} ({etrace_count/len(farms)*100:.1f}%)")
print(f"Non-E-trace farms: {non_etrace_count} ({non_etrace_count/len(farms)*100:.1f}%)")

# Print a few examples
print("\nSample entries:")
for farm in farms[:5]:
    print(f"ID: {farm['id']}")
    print(f"Name: {farm['name']}")
    print(f"Owner: {farm['owner']}")
    print(f"Address: {farm['address']}")
    print(f"Uses E-trace: {farm['uses_etrace']}")
    print("-" * 50)
