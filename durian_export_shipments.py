import uuid
import random
import csv
from datetime import datetime, timedelta

# Configuration
TOTAL_RECORDS = 20000  # Configurable number of shipment records
PACKING_HOUSES_CSV = 'durian_packing_houses.csv'
OUTPUT_CSV = 'durian_export_shipments.csv'

# Each packing house will have individual rejection rates between 75% - 90%
# This will be generated per packing house, not a global distribution

# General export rejection reasons in both English and Thai (limited to 5 total as requested)
# Ordered by expected frequency (most common first) with weighted distribution:
# 1. Incomplete documentation (35%) - Most common issue
# 2. Contains harmful chemicals (25%) - Second most common
# 3. Quality below standards (20%) - Third most common
# 4. Improper packaging (15%) - Fourth most common
# 5. Failed inspection (5%) - Least common
REJECT_REASONS = [
    {
        'english': 'Incomplete documentation',
        'thai': 'เอกสารไม่ครบถ้วน'
    },
    {
        'english': 'Contains harmful chemicals',
        'thai': 'พบสารเคมีที่เป็นอันตราย'
    },
    {
        'english': 'Quality below standards',
        'thai': 'คุณภาพต่ำกว่ามาตรฐาน'
    },
    {
        'english': 'Improper packaging',
        'thai': 'บรรจุภัณฑ์ไม่เหมาะสม'
    },
    {
        'english': 'Failed inspection',
        'thai': 'ตรวจสอบไม่ผ่าน'
    }
]

def load_packing_house_ids():
    """Load packing house IDs from the existing CSV file"""
    packing_house_ids = []
    try:
        with open(PACKING_HOUSES_CSV, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                packing_house_ids.append(row['id'])
        print(f"Loaded {len(packing_house_ids)} packing house IDs")
        return packing_house_ids
    except FileNotFoundError:
        print(f"Error: {PACKING_HOUSES_CSV} not found. Please run durian_packing_houses.py first.")
        return []

def generate_packing_house_profiles(packing_house_ids):
    """Generate unique rejection rates and reason profiles for each packing house"""
    profiles = {}

    # Define different rejection reason profile types
    reason_profile_types = [
        # Documentation-heavy problems (poor paperwork management)
        [0.50, 0.20, 0.15, 0.10, 0.05],  # 50% documentation issues
        [0.45, 0.25, 0.15, 0.10, 0.05],  # 45% documentation issues

        # Chemical contamination problems (poor safety protocols)
        [0.15, 0.45, 0.20, 0.15, 0.05],  # 45% chemical issues
        [0.20, 0.40, 0.20, 0.15, 0.05],  # 40% chemical issues

        # Quality control problems (poor production standards)
        [0.20, 0.15, 0.45, 0.15, 0.05],  # 45% quality issues
        [0.25, 0.20, 0.35, 0.15, 0.05],  # 35% quality issues

        # Packaging problems (poor packaging processes)
        [0.20, 0.20, 0.20, 0.35, 0.05],  # 35% packaging issues
        [0.25, 0.25, 0.15, 0.30, 0.05],  # 30% packaging issues

        # Inspection failure problems (poor final checks)
        [0.25, 0.25, 0.25, 0.15, 0.10],  # 10% inspection failures
        [0.30, 0.20, 0.20, 0.20, 0.10],  # 10% inspection failures

        # Balanced profiles (multiple issues)
        [0.30, 0.25, 0.25, 0.15, 0.05],  # Balanced 1
        [0.35, 0.20, 0.20, 0.20, 0.05],  # Balanced 2
        [0.25, 0.30, 0.25, 0.15, 0.05],  # Balanced 3
    ]

    # Generate profiles for each packing house
    for packing_house_id in packing_house_ids:
        # Generate rejection rate between 75% - 90%
        rejection_rate = random.uniform(0.75, 0.90)

        # Generate success rate between 5% - 20% (remaining after rejection and pending)
        success_rate = random.uniform(0.05, min(0.20, 1.0 - rejection_rate - 0.05))

        # Pending rate is the remainder (typically 5% - 10%)
        pending_rate = 1.0 - rejection_rate - success_rate

        # Assign random rejection reason profile
        reason_weights = random.choice(reason_profile_types)

        profiles[packing_house_id] = {
            'rejection_rate': rejection_rate,
            'success_rate': success_rate,
            'pending_rate': pending_rate,
            'reason_weights': reason_weights
        }

    return profiles

def generate_timestamp_last_12_months():
    """Generate a random timestamp within the last 12 months"""
    now = datetime.now()
    twelve_months_ago = now - timedelta(days=365)

    # Generate random time between 12 months ago and now
    time_diff = now - twelve_months_ago
    random_seconds = random.randint(0, int(time_diff.total_seconds()))
    random_time = twelve_months_ago + timedelta(seconds=random_seconds)

    # Convert to Unix timestamp
    return int(random_time.timestamp())

def determine_shipment_status(packing_house_id, packing_house_profiles):
    """Determine shipment status based on packing house-specific rates (75% - 90% rejection)"""
    profile = packing_house_profiles.get(packing_house_id)
    if not profile:
        # Fallback if profile not found
        profile = {
            'success_rate': 0.10,
            'rejection_rate': 0.85,
            'pending_rate': 0.05,
            'reason_weights': [0.35, 0.25, 0.20, 0.15, 0.05]
        }

    rand = random.random()

    if rand < profile['success_rate']:
        return 'succeed', None, None
    elif rand < profile['success_rate'] + profile['rejection_rate']:
        # Use packing house-specific rejection reason weights
        reason_weights = profile['reason_weights']
        reason = random.choices(REJECT_REASONS, weights=reason_weights, k=1)[0]
        return 'rejected', reason['english'], reason['thai']
    else:
        return 'pending', None, None

def generate_shipments(packing_house_ids, num_records):
    """Generate shipment records"""
    if not packing_house_ids:
        return []

    # Generate unique profiles for each packing house (rejection rates + reason weights)
    packing_house_profiles = generate_packing_house_profiles(packing_house_ids)

    shipments = []

    print(f"Generating {num_records} shipment records...")
    print(f"Generated {len(packing_house_profiles)} unique profiles for packing houses")

    # Print rejection rate summary
    rejection_rates = [profile['rejection_rate'] for profile in packing_house_profiles.values()]
    print(f"Rejection rates range: {min(rejection_rates)*100:.1f}% - {max(rejection_rates)*100:.1f}%")

    for i in range(num_records):
        if i % 1000 == 0:
            print(f"Generated {i}/{num_records} records...")

        # Generate shipment ID
        shipment_id = str(uuid.uuid4())

        # Select random packing house
        packing_house_id = random.choice(packing_house_ids)

        # Determine status and reject reasons using packing house-specific profile
        status, reject_reason_en, reject_reason_th = determine_shipment_status(packing_house_id, packing_house_profiles)

        # Generate timestamp
        timestamp = generate_timestamp_last_12_months()

        shipment = {
            'shipment_id': shipment_id,
            'packing_house_id': packing_house_id,
            'status': status,
            'reject_reason_english': reject_reason_en if reject_reason_en else '',
            'reject_reason_thai': reject_reason_th if reject_reason_th else '',
            'timestamp': timestamp
        }

        shipments.append(shipment)

    return shipments, packing_house_profiles

def save_shipments_to_csv(shipments, filename):
    """Save shipments to CSV file"""
    fieldnames = ['shipment_id', 'packing_house_id', 'status', 'reject_reason_english', 'reject_reason_thai', 'timestamp']

    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()

        for shipment in shipments:
            writer.writerow(shipment)

def print_statistics(shipments):
    """Print statistics about generated shipments"""
    total = len(shipments)
    succeed_count = sum(1 for s in shipments if s['status'] == 'succeed')
    rejected_count = sum(1 for s in shipments if s['status'] == 'rejected')
    pending_count = sum(1 for s in shipments if s['status'] == 'pending')

    print(f"\n=== Shipment Statistics ===")
    print(f"Total shipments: {total:,}")
    print(f"Successful: {succeed_count:,} ({succeed_count/total*100:.1f}%)")
    print(f"Rejected: {rejected_count:,} ({rejected_count/total*100:.1f}%)")
    print(f"Pending: {pending_count:,} ({pending_count/total*100:.1f}%)")

    # Rejection reason breakdown
    if rejected_count > 0:
        print(f"\n=== Rejection Reasons (English) - Weighted Distribution ===")
        reason_counts_en = {}
        for shipment in shipments:
            if shipment['status'] == 'rejected' and shipment['reject_reason_english']:
                reason = shipment['reject_reason_english']
                reason_counts_en[reason] = reason_counts_en.get(reason, 0) + 1

        # Sort by count descending to show the weighted distribution
        sorted_reasons = sorted(reason_counts_en.items(), key=lambda x: x[1], reverse=True)
        for reason, count in sorted_reasons:
            percentage = count/rejected_count*100
            print(f"- {reason}: {count:,} ({percentage:.1f}%)")

        print(f"\n=== Rejection Reasons (Thai) - Weighted Distribution ===")
        reason_counts_th = {}
        for shipment in shipments:
            if shipment['status'] == 'rejected' and shipment['reject_reason_thai']:
                reason = shipment['reject_reason_thai']
                reason_counts_th[reason] = reason_counts_th.get(reason, 0) + 1

        # Sort by count descending
        sorted_reasons_th = sorted(reason_counts_th.items(), key=lambda x: x[1], reverse=True)
        for reason, count in sorted_reasons_th:
            percentage = count/rejected_count*100
            print(f"- {reason}: {count:,} ({percentage:.1f}%)")

        print(f"\n=== Expected vs Actual Distribution ===")
        expected_weights = [0.35, 0.25, 0.20, 0.15, 0.05]
        reason_names = [r['english'] for r in REJECT_REASONS]
        print("Reason | Expected % | Actual %")
        print("-" * 50)
        for i, (reason, weight) in enumerate(zip(reason_names, expected_weights)):
            actual_count = reason_counts_en.get(reason, 0)
            actual_pct = actual_count/rejected_count*100 if rejected_count > 0 else 0
            print(f"{reason[:30]:<30} | {weight*100:>8.1f}% | {actual_pct:>7.1f}%")

def analyze_packing_house_profiles(packing_house_profiles):
    """Analyze and display packing house profiles"""
    print(f"\n=== Packing House Profiles Analysis ===")

    # Show rejection rate distribution
    rejection_rates = [profile['rejection_rate'] for profile in packing_house_profiles.values()]
    print(f"Rejection Rate Distribution:")
    print(f"  Minimum: {min(rejection_rates)*100:.1f}%")
    print(f"  Maximum: {max(rejection_rates)*100:.1f}%")
    print(f"  Average: {sum(rejection_rates)/len(rejection_rates)*100:.1f}%")

    # Group packing houses by their dominant rejection reason
    profile_analysis = {}
    reason_names = [r['english'] for r in REJECT_REASONS]

    for packing_house_id, profile in packing_house_profiles.items():
        weights = profile['reason_weights']
        rejection_rate = profile['rejection_rate']

        # Find the dominant rejection reason (highest weight)
        max_weight_idx = weights.index(max(weights))
        dominant_reason = reason_names[max_weight_idx]
        dominant_percentage = weights[max_weight_idx] * 100

        if dominant_reason not in profile_analysis:
            profile_analysis[dominant_reason] = []

        profile_analysis[dominant_reason].append({
            'id': packing_house_id,
            'rejection_rate': rejection_rate * 100,
            'percentage': dominant_percentage,
            'weights': weights
        })

    # Display analysis
    for reason, houses in profile_analysis.items():
        print(f"\n{reason} - Dominant Issue ({len(houses)} packing houses):")
        for house in houses[:3]:  # Show top 3 examples
            print(f"  - House {house['id'][:8]}...: {house['rejection_rate']:.1f}% rejection rate, {house['percentage']:.1f}% {reason}")
            weights_str = " | ".join([f"{reason_names[i][:15]}: {w*100:.1f}%" for i, w in enumerate(house['weights'])])
            print(f"    Reason profile: {weights_str}")

def main():
    print("=== Durian Export Shipments Generator ===")
    print("Each packing house will have unique rejection reason ratios")

    # Load packing house IDs
    packing_house_ids = load_packing_house_ids()
    if not packing_house_ids:
        return

    # Generate shipments with packing house-specific profiles
    shipments, rejection_profiles = generate_shipments(packing_house_ids, TOTAL_RECORDS)

    if not shipments:
        print("No shipments generated.")
        return

    # Save to CSV
    save_shipments_to_csv(shipments, OUTPUT_CSV)
    print(f"\nShipments saved to {OUTPUT_CSV}")

    # Print statistics
    print_statistics(shipments)

    # Analyze packing house profiles
    analyze_packing_house_profiles(rejection_profiles)

    # Show sample records
    print(f"\n=== Sample Records ===")
    for i, shipment in enumerate(shipments[:5]):
        print(f"Shipment {i+1}:")
        print(f"  ID: {shipment['shipment_id']}")
        print(f"  Packing House: {shipment['packing_house_id']}")
        print(f"  Status: {shipment['status']}")
        if shipment['reject_reason_english']:
            print(f"  Reject Reason (EN): {shipment['reject_reason_english']}")
        if shipment['reject_reason_thai']:
            print(f"  Reject Reason (TH): {shipment['reject_reason_thai']}")
        # Convert timestamp to readable format for display
        readable_time = datetime.fromtimestamp(shipment['timestamp']).strftime('%Y-%m-%d %H:%M:%S')
        print(f"  Timestamp: {shipment['timestamp']} ({readable_time})")
        print("-" * 50)

if __name__ == "__main__":
    main()
